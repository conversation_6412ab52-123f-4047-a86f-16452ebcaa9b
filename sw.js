const VERSION = '0.6.2';
const DATETIME = '2025-05-27T12:33:25.845Z';
const CACHE_NAME = `clash-strategic-webapp-${VERSION}`;

// Recursos críticos que se cachean primero (archivos individuales + carpetas críticas)
const criticalResources = [
  // Archivos individuales críticos
  'Frontend/src/js/main.js',
  'Frontend/src/css/main.css',
  'index.html',
  'home.html'
];

// Carpetas críticas (JS y CSS base)
const criticalDirectories = [
  'Frontend/src/js/config',
  'Frontend/src/js/models',
  'Frontend/src/css/base'
];

// Recursos importantes que se cachean después
const importantResources = [
  'installsw.js',
  'error404.html',
  'error500.html',
  'maintenance.html'
];

// Carpetas importantes (funcionalidad core)
const importantDirectories = [
  'Frontend/src/js/events',
  'Frontend/src/js/tools',
  'Frontend/src/js/utilsjs',
  'Frontend/src/css/objects'
];

// Recursos menos críticos
const secondaryResources = [
  'PrivacyPolicy.html',
  'RefundPolicy.html',
  'TermsService.html'
];

// Carpetas secundarias (UI y media)
const secondaryDirectories = [
  'Frontend/src/css/skins',
  'Frontend/static/media/styles/logo',
  'Frontend/static/media/styles/modal',
  'Frontend/static/media/styles/icons/menu_opc',
  'Frontend/static/media/styles/icons/card_stat_inf',
  'Frontend/static/media/styles/audio',
  'Frontend/static/media/cards/iconCards',
  'Frontend/static/media/cards/towerCards'
];

// Configuración de concurrencia
const CONCURRENT_REQUESTS = 6; // Número máximo de requests simultáneos

function addCacheBuster(url) {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}v=${VERSION}`;
}

// Extensiones permitidas para cachear
const allowedExtensions = ['jpg', 'webp', 'png', 'gif', 'mp3', 'js', 'css', 'html'];

// Función para escanear archivos en una carpeta
async function scanDirectory(dirPath) {
  try {
    const response = await fetch(dirPath + '/');
    if (!response.ok) {
      console.warn(`[SW] No se pudo acceder a la carpeta: ${dirPath}`);
      return [];
    }

    const html = await response.text();
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const links = doc.querySelectorAll('a[href]');

    const files = [];
    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href && href !== '../' && !href.startsWith('?')) {
        const fileName = href.endsWith('/') ? href.slice(0, -1) : href;
        const extension = fileName.split('.').pop()?.toLowerCase();

        if (extension && allowedExtensions.includes(extension)) {
          files.push(`${dirPath}/${fileName}`);
        }
      }
    });

    return files;
  } catch (error) {
    console.warn(`[SW] Error escaneando carpeta ${dirPath}:`, error);
    return [];
  }
}

// Función para obtener todos los archivos de las carpetas categorizadas
async function getAllCategorizedFiles() {
  const results = {
    critical: [...criticalResources],
    important: [...importantResources],
    secondary: [...secondaryResources]
  };

  try {
    // Escanear carpetas críticas
    console.log('[SW] Escaneando carpetas críticas...');
    for (const dir of criticalDirectories) {
      const files = await scanDirectory(dir);
      results.critical.push(...files);
    }

    // Escanear carpetas importantes
    console.log('[SW] Escaneando carpetas importantes...');
    for (const dir of importantDirectories) {
      const files = await scanDirectory(dir);
      results.important.push(...files);
    }

    // Escanear carpetas secundarias
    console.log('[SW] Escaneando carpetas secundarias...');
    for (const dir of secondaryDirectories) {
      const files = await scanDirectory(dir);
      results.secondary.push(...files);
    }

    const totalFiles = results.critical.length + results.important.length + results.secondary.length;
    console.log(`[SW] Total de archivos encontrados: ${totalFiles} (Críticos: ${results.critical.length}, Importantes: ${results.important.length}, Secundarios: ${results.secondary.length})`);

    return results;
  } catch (error) {
    console.error('[SW] Error obteniendo archivos categorizados:', error);
    throw error;
  }
}

function sendProgressUpdate(progress, msg) {
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({ type: 'progress', progress, msg });
    });
  });
}

// Función optimizada para cachear archivos en paralelo con límite de concurrencia
async function cacheResourcesConcurrently(cache, urls, options = {}) {
  const {
    concurrency = CONCURRENT_REQUESTS,
    useNoCacheHeaders = false,
    progressCallback = null,
    baseProgress = 0,
    progressRange = 100
  } = options;

  const results = [];
  const errors = [];

  // Procesar URLs en chunks para controlar la concurrencia
  for (let i = 0; i < urls.length; i += concurrency) {
    const chunk = urls.slice(i, i + concurrency);

    const chunkPromises = chunk.map(async (url) => {
      try {
        const urlWithCacheBuster = addCacheBuster(url);
        const requestOptions = {
          cache: 'reload' // Más eficiente que no-cache
        };

        if (useNoCacheHeaders) {
          requestOptions.headers = {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          };
        }

        const response = await fetch(urlWithCacheBuster, requestOptions);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Guardar con la URL original para que el fetch funcione
        await cache.put(url, response.clone());
        return { url, success: true };

      } catch (error) {
        console.warn(`[SW] Error cacheando ${url}:`, error.message);
        errors.push({ url, error: error.message });
        return { url, success: false, error: error.message };
      }
    });

    const chunkResults = await Promise.allSettled(chunkPromises);
    results.push(...chunkResults.map(r => r.value || r.reason));

    // Actualizar progreso si se proporciona callback
    if (progressCallback) {
      const progress = baseProgress + ((i + chunk.length) / urls.length) * progressRange;
      progressCallback(Math.min(progress, baseProgress + progressRange));
    }
  }

  return { results, errors, successCount: results.filter(r => r.success).length };
}

self.addEventListener('install', event => {
  console.log('[SW] Instalando versión:', VERSION, 'compilada el:', DATETIME);
  self.skipWaiting();

  event.waitUntil(
    (async () => {
      try {
        // Limpiar cache anterior
        await caches.delete(CACHE_NAME);
        console.log('[SW] Cache anterior eliminado, creando nuevo cache:', CACHE_NAME);

        const cache = await caches.open(CACHE_NAME);
        console.log('[SW] Cache abierto:', CACHE_NAME);

        // Fase 1: Obtener todos los archivos categorizados
        sendProgressUpdate(5, 'Escaneando carpetas y categorizando archivos...');
        const categorizedFiles = await getAllCategorizedFiles();

        // Fase 2: Cachear recursos críticos (25% del progreso)
        sendProgressUpdate(15, `Cacheando ${categorizedFiles.critical.length} recursos críticos...`);
        const criticalResult = await cacheResourcesConcurrently(cache, categorizedFiles.critical, {
          concurrency: 4,
          useNoCacheHeaders: true,
          progressCallback: (progress) => sendProgressUpdate(15 + progress * 0.25),
          baseProgress: 15,
          progressRange: 25
        });
        console.log(`[SW] Recursos críticos: ${criticalResult.successCount}/${categorizedFiles.critical.length} cacheados`);

        // Fase 3: Cachear recursos importantes (30% del progreso)
        sendProgressUpdate(40, `Cacheando ${categorizedFiles.important.length} recursos importantes...`);
        const importantResult = await cacheResourcesConcurrently(cache, categorizedFiles.important, {
          concurrency: CONCURRENT_REQUESTS,
          useNoCacheHeaders: true,
          progressCallback: (progress) => sendProgressUpdate(40 + progress * 0.3),
          baseProgress: 40,
          progressRange: 30
        });
        console.log(`[SW] Recursos importantes: ${importantResult.successCount}/${categorizedFiles.important.length} cacheados`);

        // Fase 4: Cachear recursos secundarios (30% del progreso)
        sendProgressUpdate(70, `Cacheando ${categorizedFiles.secondary.length} recursos secundarios...`);
        const secondaryResult = await cacheResourcesConcurrently(cache, categorizedFiles.secondary, {
          concurrency: CONCURRENT_REQUESTS,
          progressCallback: (progress) => sendProgressUpdate(70 + progress * 0.3),
          baseProgress: 70,
          progressRange: 30
        });
        console.log(`[SW] Recursos secundarios: ${secondaryResult.successCount}/${categorizedFiles.secondary.length} cacheados`);

        const totalCached = criticalResult.successCount + importantResult.successCount + secondaryResult.successCount;
        const totalFiles = categorizedFiles.critical.length + categorizedFiles.important.length + categorizedFiles.secondary.length;
        const totalErrors = criticalResult.errors.length + importantResult.errors.length + secondaryResult.errors.length;

        console.log(`[SW] Instalación completada. ${totalCached}/${totalFiles} recursos cacheados exitosamente`);
        if (totalErrors > 0) {
          console.warn(`[SW] ${totalErrors} archivos no se pudieron cachear`);
        }

        sendProgressUpdate(100, `Instalación completada. ${totalCached}/${totalFiles} recursos cacheados`);

      } catch (err) {
        console.error('[SW] Error durante la instalación:', err);
        sendProgressUpdate(0, 'Error al cachear los recursos');
        throw err;
      }
    })()
  );
});

self.addEventListener('activate', event => {
  console.log('[SW] Activando versión:', VERSION, 'compilada el:', DATETIME);
  event.waitUntil(
    caches.keys().then(cacheNames => {
      console.log('[SW] Caches encontrados:', cacheNames);
      return Promise.all(
        cacheNames
          .filter(name => name !== CACHE_NAME && name.startsWith('clash-strategic-webapp-'))
          .map(name => {
            console.log('[SW] Borrando cache antiguo:', name);
            return caches.delete(name);
          })
      );
    }).then(() => {
      console.log('[SW] Caches antiguos borrados. Reclamando clientes...');
      return self.clients.claim();
    }).then(() => {
      console.log('[SW] Clientes reclamados.');
      return self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({ type: 'ACTIVATED', version: VERSION, datetime: DATETIME });
        });
        console.log(`[SW] Notificación enviada a ${clients.length} clientes`);
      });
    }).catch(err => {
      console.error('[SW] Error durante la activación:', err);
    })
  );
});

self.addEventListener('fetch', event => {
  // Optimización: Solo interceptar requests que nos interesan
  const url = new URL(event.request.url);

  // Ignorar requests externos, chrome-extension, etc.
  if (!url.origin.includes(self.location.origin) &&
    !url.pathname.startsWith('/') &&
    !url.protocol.startsWith('http')) {
    return;
  }

  event.respondWith(
    (async () => {
      try {
        // Intentar obtener desde cache primero
        const cachedResponse = await caches.match(event.request);
        if (cachedResponse) {
          // console.log('[SW] Sirviendo desde cache:', event.request.url);
          return cachedResponse;
        }

        // Si no está en cache, obtener de la red
        // console.log('[SW] Obteniendo de la red:', event.request.url);
        const networkResponse = await fetch(event.request);

        // Opcionalmente cachear la respuesta si es exitosa
        if (networkResponse.ok && event.request.method === 'GET') {
          const cache = await caches.open(CACHE_NAME);
          cache.put(event.request, networkResponse.clone()).catch(err => {
            // No bloquear si falla el cacheo
            console.warn('[SW] Error al cachear respuesta de red:', err);
          });
        }

        return networkResponse;

      } catch (err) {
        console.error('[SW] Error al obtener de la red:', event.request.url, err);

        // Fallback para documentos HTML
        if (event.request.destination === 'document') {
          const errorPage = await caches.match('/error404.html');
          if (errorPage) {
            return errorPage;
          }
        }

        throw err;
      }
    })()
  );
});

self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
