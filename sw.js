const VERSION = '0.6.1';
const DATETIME = '2025-05-27T12:33:25.845Z';
const CACHE_NAME = `clash-strategic-webapp-${VERSION}`;

// Recursos críticos que se cachean primero
const criticalResources = [
  'Frontend/src/js/main.js',
  'Frontend/src/css/main.css',
  'index.html',
  'home.html'
];

// Recursos importantes que se cachean después
const importantResources = [
  'installsw.js',
  'error404.html',
  'error500.html',
  'maintenance.html'
];

// Recursos menos críticos
const secondaryResources = [
  'PrivacyPolicy.html',
  'RefundPolicy.html',
  'TermsService.html'
];

// Configuración de concurrencia
const CONCURRENT_REQUESTS = 6; // Número máximo de requests simultáneos
const CHUNK_SIZE = 10; // Tamaño de chunks para archivos adicionales

function addCacheBuster(url) {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}v=${VERSION}`;
}

function getUrlFiles() {
  return fetch('list_url_files.php', {
    cache: 'no-cache'
  })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error: ${response.status} ${response.statusText}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) return { files: data, size: 0 };
      if (data && typeof data === 'object') return { files: Object.values(data.allFiles), size: data.sizeAll };
      throw new Error('Respuesta inesperada al obtener archivos');
    });
}

function sendProgressUpdate(progress, msg) {
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({ type: 'progress', progress, msg });
    });
  });
}

// Función optimizada para cachear archivos en paralelo con límite de concurrencia
async function cacheResourcesConcurrently(cache, urls, options = {}) {
  const {
    concurrency = CONCURRENT_REQUESTS,
    useNoCacheHeaders = false,
    progressCallback = null,
    baseProgress = 0,
    progressRange = 100
  } = options;

  const results = [];
  const errors = [];

  // Procesar URLs en chunks para controlar la concurrencia
  for (let i = 0; i < urls.length; i += concurrency) {
    const chunk = urls.slice(i, i + concurrency);

    const chunkPromises = chunk.map(async (url) => {
      try {
        const urlWithCacheBuster = addCacheBuster(url);
        const requestOptions = {
          cache: 'reload' // Más eficiente que no-cache
        };

        if (useNoCacheHeaders) {
          requestOptions.headers = {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          };
        }

        const response = await fetch(urlWithCacheBuster, requestOptions);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Guardar con la URL original para que el fetch funcione
        await cache.put(url, response.clone());
        return { url, success: true };

      } catch (error) {
        console.warn(`[SW] Error cacheando ${url}:`, error.message);
        errors.push({ url, error: error.message });
        return { url, success: false, error: error.message };
      }
    });

    const chunkResults = await Promise.allSettled(chunkPromises);
    results.push(...chunkResults.map(r => r.value || r.reason));

    // Actualizar progreso si se proporciona callback
    if (progressCallback) {
      const progress = baseProgress + ((i + chunk.length) / urls.length) * progressRange;
      progressCallback(Math.min(progress, baseProgress + progressRange));
    }
  }

  return { results, errors, successCount: results.filter(r => r.success).length };
}

self.addEventListener('install', event => {
  console.log('[SW] Instalando versión:', VERSION, 'compilada el:', DATETIME);
  self.skipWaiting();

  event.waitUntil(
    (async () => {
      try {
        // Limpiar cache anterior
        await caches.delete(CACHE_NAME);
        console.log('[SW] Cache anterior eliminado, creando nuevo cache:', CACHE_NAME);

        const cache = await caches.open(CACHE_NAME);
        console.log('[SW] Cache abierto:', CACHE_NAME);

        // Fase 1: Cachear recursos críticos (10% del progreso)
        sendProgressUpdate(5, 'Cacheando recursos críticos...');
        const criticalResult = await cacheResourcesConcurrently(cache, criticalResources, {
          concurrency: 4,
          useNoCacheHeaders: true,
          progressCallback: (progress) => sendProgressUpdate(5 + progress * 0.1),
          baseProgress: 5,
          progressRange: 10
        });
        console.log(`[SW] Recursos críticos: ${criticalResult.successCount}/${criticalResources.length} cacheados`);

        // Fase 2: Cachear recursos importantes (15% del progreso)
        sendProgressUpdate(15, 'Cacheando recursos importantes...');
        const importantResult = await cacheResourcesConcurrently(cache, importantResources, {
          concurrency: 4,
          useNoCacheHeaders: true,
          progressCallback: (progress) => sendProgressUpdate(15 + progress * 0.15),
          baseProgress: 15,
          progressRange: 15
        });
        console.log(`[SW] Recursos importantes: ${importantResult.successCount}/${importantResources.length} cacheados`);

        // Fase 3: Cachear recursos secundarios (10% del progreso)
        sendProgressUpdate(30, 'Cacheando recursos secundarios...');
        const secondaryResult = await cacheResourcesConcurrently(cache, secondaryResources, {
          concurrency: 3,
          progressCallback: (progress) => sendProgressUpdate(30 + progress * 0.1),
          baseProgress: 30,
          progressRange: 10
        });
        console.log(`[SW] Recursos secundarios: ${secondaryResult.successCount}/${secondaryResources.length} cacheados`);

        // Fase 4: Obtener lista de archivos adicionales
        sendProgressUpdate(40, 'Obteniendo lista de archivos adicionales...');
        const imageData = await getUrlFiles();
        const { files, size } = imageData;

        let additionalResult = { successCount: 0, errors: [] };

        if (files && files.length > 0) {
          const imageProgressMsg = `Cacheando ${files.length} archivos adicionales (${size?.toFixed(2) || 'desconocido'} MB)`;
          console.log(`[SW] ${imageProgressMsg}`);
          sendProgressUpdate(45, imageProgressMsg);

          // Fase 5: Cachear archivos adicionales en chunks (50% del progreso)
          additionalResult = await cacheResourcesConcurrently(cache, files, {
            concurrency: CONCURRENT_REQUESTS,
            progressCallback: (progress) => sendProgressUpdate(45 + progress * 0.5),
            baseProgress: 45,
            progressRange: 50
          });

          console.log(`[SW] Archivos adicionales: ${additionalResult.successCount}/${files.length} cacheados`);

          // Mostrar errores si los hay
          if (additionalResult.errors.length > 0) {
            console.warn(`[SW] ${additionalResult.errors.length} archivos no se pudieron cachear:`, additionalResult.errors);
          }
        }

        const totalCached = criticalResult.successCount + importantResult.successCount +
          secondaryResult.successCount + additionalResult.successCount;

        console.log('[SW] Instalación completada. Total de recursos cacheados:', totalCached);
        sendProgressUpdate(100, `Instalación completada. ${totalCached} recursos cacheados`);

      } catch (err) {
        console.error('[SW] Error durante la instalación:', err);
        sendProgressUpdate(0, 'Error al cachear los recursos');
        throw err;
      }
    })()
  );
});

self.addEventListener('activate', event => {
  console.log('[SW] Activando versión:', VERSION, 'compilada el:', DATETIME);
  event.waitUntil(
    caches.keys().then(cacheNames => {
      console.log('[SW] Caches encontrados:', cacheNames);
      return Promise.all(
        cacheNames
          .filter(name => name !== CACHE_NAME && name.startsWith('clash-strategic-webapp-'))
          .map(name => {
            console.log('[SW] Borrando cache antiguo:', name);
            return caches.delete(name);
          })
      );
    }).then(() => {
      console.log('[SW] Caches antiguos borrados. Reclamando clientes...');
      return self.clients.claim();
    }).then(() => {
      console.log('[SW] Clientes reclamados.');
      return self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({ type: 'ACTIVATED', version: VERSION, datetime: DATETIME });
        });
        console.log(`[SW] Notificación enviada a ${clients.length} clientes`);
      });
    }).catch(err => {
      console.error('[SW] Error durante la activación:', err);
    })
  );
});

self.addEventListener('fetch', event => {
  // Optimización: Solo interceptar requests que nos interesan
  const url = new URL(event.request.url);

  // Ignorar requests externos, chrome-extension, etc.
  if (!url.origin.includes(self.location.origin) &&
    !url.pathname.startsWith('/') &&
    !url.protocol.startsWith('http')) {
    return;
  }

  event.respondWith(
    (async () => {
      try {
        // Intentar obtener desde cache primero
        const cachedResponse = await caches.match(event.request);
        if (cachedResponse) {
          // console.log('[SW] Sirviendo desde cache:', event.request.url);
          return cachedResponse;
        }

        // Si no está en cache, obtener de la red
        // console.log('[SW] Obteniendo de la red:', event.request.url);
        const networkResponse = await fetch(event.request);

        // Opcionalmente cachear la respuesta si es exitosa
        if (networkResponse.ok && event.request.method === 'GET') {
          const cache = await caches.open(CACHE_NAME);
          cache.put(event.request, networkResponse.clone()).catch(err => {
            // No bloquear si falla el cacheo
            console.warn('[SW] Error al cachear respuesta de red:', err);
          });
        }

        return networkResponse;

      } catch (err) {
        console.error('[SW] Error al obtener de la red:', event.request.url, err);

        // Fallback para documentos HTML
        if (event.request.destination === 'document') {
          const errorPage = await caches.match('/error404.html');
          if (errorPage) {
            return errorPage;
          }
        }

        throw err;
      }
    })()
  );
});

self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
